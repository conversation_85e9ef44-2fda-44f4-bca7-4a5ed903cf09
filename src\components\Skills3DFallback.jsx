import { useRef, useState, useEffect } from 'react';
import { useGSAP } from '@gsap/react';
import gsap from 'gsap';

const Skills3DFallback = ({ skills }) => {
  const containerRef = useRef();
  const skillRefs = useRef([]);
  const [selectedSkill, setSelectedSkill] = useState(null);
  const [, setMousePosition] = useState({ x: 0, y: 0 });

  // Color palette for skills
  const colors = [
    '#61DAFB', // React blue
    '#F7DF1E', // JavaScript yellow
    '#68A063', // Node.js green
    '#FF2D20', // <PERSON>vel red
    '#336791', // Database blue
    '#FF6B6B'  // Design red
  ];

  useGSAP(() => {
    // Initial animation
    skillRefs.current.forEach((el, index) => {
      if (el) {
        gsap.fromTo(el, 
          { 
            scale: 0,
            rotation: 0,
            opacity: 0
          },
          {
            scale: 1,
            rotation: 360,
            opacity: 1,
            duration: 1,
            delay: index * 0.2,
            ease: "back.out(1.7)"
          }
        );

        // Continuous floating animation
        gsap.to(el, {
          y: "+=20",
          duration: 2 + index * 0.3,
          repeat: -1,
          yoyo: true,
          ease: "power2.inOut"
        });

        // Continuous rotation
        gsap.to(el.querySelector('.skill-sphere'), {
          rotation: 360,
          duration: 10 + index * 2,
          repeat: -1,
          ease: "none"
        });
      }
    });
  }, []);

  // Mouse move effect
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const x = (e.clientX - rect.left - rect.width / 2) / rect.width;
        const y = (e.clientY - rect.top - rect.height / 2) / rect.height;
        setMousePosition({ x, y });

        // Apply 3D-like transform to skills based on mouse position
        skillRefs.current.forEach((el, index) => {
          if (el) {
            const offsetX = x * 30 * (index % 2 === 0 ? 1 : -1);
            const offsetY = y * 20 * (index % 3 === 0 ? 1 : -1);
            
            gsap.to(el, {
              x: offsetX,
              y: offsetY,
              rotationY: x * 15,
              rotationX: -y * 15,
              duration: 0.3,
              ease: "power2.out"
            });
          }
        });
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
      return () => container.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  const handleSkillClick = (index) => {
    setSelectedSkill(selectedSkill === index ? null : index);
    
    // Pulse animation on click
    if (skillRefs.current[index]) {
      gsap.to(skillRefs.current[index], {
        scale: 1.2,
        duration: 0.2,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut"
      });
    }
  };

  // Position skills in a circular pattern
  const getSkillPosition = (index, total) => {
    const radius = 120;
    const angle = (index / total) * Math.PI * 2;
    return {
      x: Math.cos(angle) * radius,
      y: Math.sin(angle) * radius * 0.5
    };
  };

  return (
    <div
      ref={containerRef}
      className="w-full h-[400px] relative overflow-hidden bg-gradient-to-br from-black/20 to-black/40 rounded-lg"
      style={{ perspective: '1000px' }}
    >
      {/* Skills spheres */}
      <div className="absolute inset-0 flex items-center justify-center">
        {skills.map((skill, index) => {
          const position = getSkillPosition(index, skills.length);
          return (
            <div
              key={skill.name}
              ref={(el) => (skillRefs.current[index] = el)}
              className="absolute cursor-pointer group"
              style={{
                left: `calc(50% + ${position.x}px)`,
                top: `calc(50% + ${position.y}px)`,
                transform: 'translate(-50%, -50%)'
              }}
              onClick={() => handleSkillClick(index)}
            >
              <div
                className="skill-sphere w-20 h-20 rounded-full flex flex-col items-center justify-center text-white font-bold shadow-2xl transition-all duration-300 group-hover:scale-110"
                style={{
                  background: `linear-gradient(135deg, ${colors[index % colors.length]}, ${colors[index % colors.length]}dd)`,
                  boxShadow: `0 10px 30px ${colors[index % colors.length]}40`,
                  border: selectedSkill === index ? `3px solid ${colors[index % colors.length]}` : '3px solid transparent'
                }}
              >
                <span className="text-base font-bold">{skill.level}%</span>
                <span className="text-[10px] text-center px-1 leading-tight">{skill.name}</span>
              </div>
              
              {/* Glow effect */}
              <div 
                className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl"
                style={{
                  background: colors[index % colors.length],
                  transform: 'scale(1.5)'
                }}
              />
            </div>
          );
        })}
      </div>

      {/* Center info */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="text-center text-white/50">
          <h4 className="text-base font-medium mb-1">Interactive Skills</h4>
          <p className="text-xs">Hover and click to explore</p>
        </div>
      </div>

      {/* Skill details overlay */}
      {selectedSkill !== null && (
        <div className="absolute bottom-4 left-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white">
          <h4 className="text-lg font-bold mb-2">{skills[selectedSkill].name}</h4>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="w-full bg-white/20 rounded-full h-2">
                <div
                  className="h-2 rounded-full transition-all duration-1000"
                  style={{ 
                    width: `${skills[selectedSkill].level}%`,
                    backgroundColor: colors[selectedSkill % colors.length]
                  }}
                />
              </div>
            </div>
            <span className="font-bold">{skills[selectedSkill].level}%</span>
          </div>
          <p className="text-sm text-white/70 mt-2">
            Move your mouse around to see the 3D effect • Click skills for details
          </p>
        </div>
      )}

      {/* Instructions */}
      <div className="absolute top-4 right-4 bg-black/60 backdrop-blur-sm rounded-lg p-3 text-white text-sm">
        <p className="mb-1">🖱️ Move mouse for 3D effect</p>
        <p>👆 Click skills for details</p>
      </div>
    </div>
  );
};

export default Skills3DFallback;
