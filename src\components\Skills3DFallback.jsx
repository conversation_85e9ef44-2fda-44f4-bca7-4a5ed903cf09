import { useRef, useState, useEffect } from 'react';
import { useGSAP } from '@gsap/react';
import gsap from 'gsap';

const Skills3DFallback = ({ skills }) => {
  const containerRef = useRef();
  const marqueeRef = useRef();
  const [selectedSkill, setSelectedSkill] = useState(null);
  const [isPaused, setIsPaused] = useState(false);

  // Safety check for skills prop
  if (!skills || !Array.isArray(skills) || skills.length === 0) {
    return (
      <div className="w-full h-[400px] flex items-center justify-center text-white/50">
        <p>No skills data available</p>
      </div>
    );
  }

  // Enhanced color palette with gradients
  const skillColors = [
    { bg: 'from-blue-500 to-cyan-400', shadow: 'shadow-blue-500/30', border: 'border-blue-400' }, // React
    { bg: 'from-yellow-500 to-orange-400', shadow: 'shadow-yellow-500/30', border: 'border-yellow-400' }, // JavaScript
    { bg: 'from-green-500 to-emerald-400', shadow: 'shadow-green-500/30', border: 'border-green-400' }, // Node.js
    { bg: 'from-red-500 to-pink-400', shadow: 'shadow-red-500/30', border: 'border-red-400' }, // Laravel
    { bg: 'from-purple-500 to-indigo-400', shadow: 'shadow-purple-500/30', border: 'border-purple-400' }, // Database
    { bg: 'from-pink-500 to-rose-400', shadow: 'shadow-pink-500/30', border: 'border-pink-400' }, // Design
  ];

  // Duplicate skills for seamless marquee
  const duplicatedSkills = [...skills, ...skills, ...skills];

  useGSAP(() => {
    if (!marqueeRef.current) return;

    // Create seamless marquee animation
    const marqueeAnimation = gsap.to(marqueeRef.current, {
      x: "-33.333%", // Move by 1/3 since we have 3 copies
      duration: 20,
      ease: "none",
      repeat: -1,
    });

    // Pause/resume functionality
    const handleMouseEnter = () => {
      setIsPaused(true);
      gsap.to(marqueeRef.current, { timeScale: 0.1, duration: 0.5 });
    };

    const handleMouseLeave = () => {
      setIsPaused(false);
      gsap.to(marqueeRef.current, { timeScale: 1, duration: 0.5 });
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mouseenter', handleMouseEnter);
      container.addEventListener('mouseleave', handleMouseLeave);
    }

    // Cleanup function
    return () => {
      marqueeAnimation.kill();
      if (container) {
        container.removeEventListener('mouseenter', handleMouseEnter);
        container.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Kill any remaining GSAP animations
      if (marqueeRef.current) {
        gsap.killTweensOf(marqueeRef.current);
      }
    };
  }, []);

  const handleSkillClick = (skillIndex, copyIndex) => {
    const uniqueIndex = `${skillIndex}-${copyIndex}`;
    setSelectedSkill(selectedSkill === uniqueIndex ? null : uniqueIndex);
  };

  return (
    <div
      ref={containerRef}
      className="w-full h-[300px] relative overflow-hidden bg-gradient-to-br from-black/20 to-black/40 rounded-lg cursor-pointer"
    >
      {/* Marquee Container */}
      <div
        ref={marqueeRef}
        className="flex items-center h-full gap-8 will-change-transform"
        style={{ width: '300%' }} // 3x width for 3 copies
      >
        {/* Render 3 copies of skills for seamless loop */}
        {[0, 1, 2].map((copyIndex) => (
          <div key={copyIndex} className="flex items-center gap-8 min-w-full">
            {skills.map((skill, skillIndex) => {
              const colorIndex = skillIndex % skillColors.length;
              const uniqueKey = `${skill.name}-${copyIndex}`;
              const isSelected = selectedSkill === `${skillIndex}-${copyIndex}`;

              return (
                <div
                  key={uniqueKey}
                  className="flex-shrink-0 group relative"
                  onClick={() => handleSkillClick(skillIndex, copyIndex)}
                >
                  {/* Skill Card */}
                  <div className={`
                    relative bg-gradient-to-br ${skillColors[colorIndex].bg}
                    rounded-2xl p-6 min-w-[200px] h-[180px]
                    flex flex-col justify-between
                    transform transition-all duration-500 ease-out
                    hover:scale-105 hover:-translate-y-2
                    ${skillColors[colorIndex].shadow}
                    ${isSelected ? 'scale-105 -translate-y-2' : ''}
                    cursor-pointer border-2 ${isSelected ? skillColors[colorIndex].border : 'border-transparent'}
                  `}>
                    {/* Skill Name */}
                    <div>
                      <h3 className="text-white font-bold text-lg mb-2 leading-tight">
                        {skill.name}
                      </h3>
                      <div className="w-12 h-1 bg-white/30 rounded-full"></div>
                    </div>

                    {/* Skill Level */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-white/80 text-sm font-medium">Proficiency</span>
                        <span className="text-white font-bold text-2xl">{skill.level}%</span>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-white/20 rounded-full h-2 overflow-hidden">
                        <div
                          className="h-full bg-white rounded-full transition-all duration-1000 ease-out"
                          style={{ width: `${skill.level}%` }}
                        />
                      </div>
                    </div>

                    {/* Hover Glow Effect */}
                    <div className="absolute inset-0 bg-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* Corner Accent */}
                    <div className="absolute top-4 right-4 w-3 h-3 bg-white/30 rounded-full" />
                  </div>

                  {/* Floating Particles Effect */}
                  <div className="absolute inset-0 pointer-events-none">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className={`absolute w-1 h-1 bg-white/40 rounded-full animate-pulse`}
                        style={{
                          top: `${20 + i * 30}%`,
                          right: `${10 + i * 15}%`,
                          animationDelay: `${i * 0.5}s`,
                          animationDuration: '2s'
                        }}
                      />
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        ))}
      </div>

      {/* Gradient Overlays for smooth edges */}
      <div className="absolute left-0 top-0 w-20 h-full bg-gradient-to-r from-black/40 to-transparent pointer-events-none z-10" />
      <div className="absolute right-0 top-0 w-20 h-full bg-gradient-to-l from-black/40 to-transparent pointer-events-none z-10" />

      {/* Instructions */}
      <div className="absolute top-4 left-4 bg-black/60 backdrop-blur-sm rounded-lg p-3 text-white text-sm z-20">
        <p className="mb-1">🎯 Hover to slow down</p>
        <p>👆 Click cards for details</p>
      </div>

      {/* Selected Skill Details */}
      {selectedSkill && (
        <div className="absolute bottom-4 left-4 right-4 bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white z-20">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-bold">Skill Details</h4>
            <button
              onClick={() => setSelectedSkill(null)}
              className="text-white/60 hover:text-white transition-colors"
            >
              ✕
            </button>
          </div>
          <p className="text-white/70 text-sm mt-2">
            {isPaused ? "Marquee paused - move mouse away to resume" : "Smooth marquee animation with interactive cards"}
          </p>
        </div>
      )}
    </div>
  );
};

export default Skills3DFallback;
