{"name": "arone-fritz-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@gsap/react": "^2.1.2", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@tailwindcss/vite": "^4.1.7", "gsap": "^3.13.0", "lenis": "^1.3.4", "maath": "^0.10.8", "react": "^19.1.0", "react-dom": "^19.1.0", "react-responsive": "^10.0.1", "react-scroll": "^1.9.3", "tailwindcss": "^4.1.7", "three": "^0.176.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@iconify/react": "^6.0.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}