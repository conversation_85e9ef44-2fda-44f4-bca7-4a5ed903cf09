@import "tailwindcss";

/* Amiamie Regular */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-Regular.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* Amiamie Regular Italic */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-Italic.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-Italic.ttf") format("truetype");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

/* Amiamie Light */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-Light.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

/* Amiamie Light Italic */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-LightItalic.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-LightItalic.ttf") format("truetype");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

/* Amiamie Black */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-Black.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-Black.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Amiamie Black Italic */
@font-face {
  font-family: "Amiamie";
  src: url("/fonts/amiamie/otf/Amiamie-BlackItalic.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-BlackItalic.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

/* Amiamie-Round Variants (if needed separately) */
@font-face {
  font-family: "Amiamie-Round";
  src: url("/fonts/amiamie/otf/Amiamie-RegularRound.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-RegularRound.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Amiamie-Round";
  src: url("/fonts/amiamie/otf/Amiamie-BlackRound.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-BlackRound.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Amiamie-Round";
  src: url("/fonts/amiamie/otf/Amiamie-BlackItalicRound.otf") format("opentype"),
    url("/fonts/amiamie/ttf/Amiamie-BlackItalicRound.ttf") format("truetype");
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}
@theme {
  --color-primary: #e5e5e0;
  --color-DarkLava: #393632;
  --color-SageGray: #8b8b73;
  --color-gold: #cfa355;
  --font-amiamie: "Amiamie", sans-serif;
  --font-amiamie-round: "Amiamie-Round", sans-serif;

  --animate-marquee: marquee 40s infinite linear;
  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }
}

body {
  background: #e5e5e0;
  color: black;
  scroll-behavior: smooth;
  overflow-x: hidden;
  font-family: var(--font-amiamie);
}

@utility clip-path {
  clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0 100%);
}

@utility banner-text-responsive {
  @apply text-[52px] sm:text-[82px] md:text-[102px] lg:text-[122px] xl:text-[133px] leading-9 sm:leading-16 lg:leading-20;
}

@utility value-text-responsive {
  @apply text-2xl md:text-[26px] lg:text-[32px];
}

@utility marquee-text-responsive {
  @apply text-[28px] sm:text-[36px] lg:text-[42px];
}

@utility contact-text-responsive {
  @apply text-[42px] sm:text-[52px] md:text-[62px] lg:text-[100px];
}

/* Loading Screen Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 30px rgba(147, 51, 234, 0.7); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

.loading-text {
  animation: float 3s ease-in-out infinite;
}

.loading-progress {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.pulse-particle {
  animation: pulse-glow 2s ease-in-out infinite;
}